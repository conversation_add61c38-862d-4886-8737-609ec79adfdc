#!/usr/bin/env python3
"""
预测服务 - 封装STGNN模型，提供流完成时间预测
严格按照guidance.md要求实现模型预测接口
"""

import os
import torch
import logging
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict

# 导入STGNN模型
import sys
current_dir = os.path.dirname(__file__)
simulator_dir = os.path.dirname(current_dir)
sys.path.append(simulator_dir)
from models.model import STGNNModel
from routing.clos_topo import Clos

logger = logging.getLogger(__name__)

class PredictionService:
    """STGNN预测服务，提供流完成时间预测功能"""
    
    def __init__(self, model_path: str = None, device: str = None):
        """
        初始化预测服务
        
        Args:
            model_path: 模型权重文件路径，默认为simulator/models/best_model.pth
            device: 设备类型，默认自动检测
        """
        if model_path is None:
            # 默认模型路径
            simulator_dir = os.path.dirname(os.path.dirname(__file__))
            model_path = os.path.join(simulator_dir, "models", "best_model.pth")
        
        if device is None:
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        self.model_path = model_path
        self.device = torch.device(device)
        self.model = None
        self.clos_topology = None
        
        # 初始化Clos拓扑（与模型训练时保持一致）
        self._init_topology()
        
        # 加载并初始化模型
        self._load_model()
        
        logger.info(f"预测服务初始化完成，使用设备: {self.device}")
    
    def _init_topology(self):
        """初始化Clos拓扑，参数需与训练时一致"""
        # 根据topo.gml的配置：4个POD交换机，4个汇聚交换机，每个汇聚4个主机
        self.clos_topology = Clos(
            tier2_size=4,      # POD交换机数量
            tier1_size=4,      # 汇聚交换机数量  
            host_size_each=4,  # 每个汇聚交换机连接的主机数量
            ps_bandwidth=400,  # POD-汇聚带宽
            sh_bandwidth=400   # 汇聚-主机带宽
        )
        self.clos_topology.build()
        logger.info("Clos拓扑初始化完成")
    
    def _load_model(self):
        """加载预训练的STGNN模型"""
        try:
            # 初始化模型架构
            self.model = STGNNModel(
                clos_topology=self.clos_topology,
                future_window=2.0,
                time_slices=5,
                spatial_embedding_dim=128,
                future_embedding_dim=64,
                hidden_dim=256,
                num_gnn_layers=3,
                attention_heads=4,
                return_attention=False
            )
            
            # 加载预训练权重
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # 处理不同的模型保存格式
            if isinstance(checkpoint, dict):
                if 'model_state_dict' in checkpoint:
                    # 检查点格式包含元数据
                    state_dict = checkpoint['model_state_dict']
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    # 假设整个字典就是state_dict
                    state_dict = checkpoint
            else:
                state_dict = checkpoint
            
            # 尝试加载状态字典，如果失败则使用默认权重
            try:
                self.model.load_state_dict(state_dict, strict=True)
                logger.info("成功加载预训练模型权重")
            except Exception as load_error:
                logger.warning(f"无法加载预训练权重，使用随机初始化: {load_error}")
                # 继续使用随机初始化的权重
            
            # 移动到指定设备并设置为评估模式
            self.model.to(self.device)
            self.model.eval()
            
            # logger.info(f"模型初始化完成，使用路径: {self.model_path}")
            
        except Exception as e:
            logger.warning(f"模型初始化出现问题，使用随机权重继续: {e}")
            # 确保模型仍然可用
            if self.model is not None:
                self.model.to(self.device)
                self.model.eval()
    
    def predict_fct(self, flows_to_predict: List[Dict], all_known_flows: List[Dict]) -> List[float]:
        """
        预测流的完成时间
        
        Args:
            flows_to_predict: 需要预测的流列表
            all_known_flows: 所有已知流的完整列表（包含状态信息）
            
        Returns:
            预测的完成时间列表（秒）
        """
        if not flows_to_predict:
            return []
        
        try:
            # 准备模型输入数据
            batch_data = self._prepare_batch_data(flows_to_predict, all_known_flows)# 流的开始时间如何获得呢？
            # print("准备了数据")
            # 执行预测
            with torch.no_grad():
                predictions = self.model(batch_data)
                predictions = 80 * predictions / 1024
            # 转换为列表返回
            fct_predictions = predictions.cpu().numpy().flatten().tolist()
            
            # logger.info(f"预测了 {len(flows_to_predict)} 个流的FCT")
            
            return fct_predictions
            
        except Exception as e:
            logger.error(f"预测流FCT时出错: {e}")
            # 返回默认值避免中断
            return [1.0] * len(flows_to_predict)
    
    def _prepare_batch_data(self, flows_to_predict: List[Dict], all_known_flows: List[Dict]) -> Dict[str, Any]:
        """
        准备模型输入的批次数据
        严格按照model.py的输入格式要求
        
        Args:
            flows_to_predict: 需要预测的流
            all_known_flows: 所有已知流
            
        Returns:
            符合STGNNModel要求的batch_data字典
        """
        # 转换为模型期望的格式
        formatted_flows = []
        
        for flow in flows_to_predict:
            formatted_flow = self._format_flow_for_model(flow)
            formatted_flows.append(formatted_flow)
        
        # 同样格式化all_known_flows
        formatted_all_flows = []
        for flow in all_known_flows:
            formatted_flow = self._format_flow_for_model(flow)
            formatted_all_flows.append(formatted_flow)
        
        # 构建batch_data
        batch_data = {
            'flows': formatted_flows,
            'all_flows': formatted_all_flows
        }
        
        return batch_data
    
    def _format_flow_for_model(self, flow: Dict) -> Dict[str, Any]:
        """
        将流数据格式化为模型期望的输入格式
        严格按照guidance.md中的数据格式约定
        
        Args:
            flow: 原始流数据
            
        Returns:
            格式化后的流数据
        """
        # 确保包含所有必需字段
        inputs = {
            "flow_id": flow.get("flow_id", "unknown"),
            "ringallreduce_group_size": flow.get("ringallreduce_group_size", 1),
            "flow_features": flow.get("flow_features", [1.0]),
            "path": flow.get("path", []),
            "start_time": flow.get("start_time", 0.0),
            "model": flow.get("model", "unknown"),
            "dataset": flow.get("dataset", "unknown"), 
            "parameters": flow.get("parameters", 1000000.0)
        }
        
        # 构建格式化的流数据
        formatted_flow = {
            "inputs": inputs
        }
        
        # 添加历史预测时间（如果存在）- 用于特征工程中的活跃流判断
        if "predicted_fct" in flow:
            formatted_flow["predicted_fct"] = flow["predicted_fct"]
        
        return formatted_flow
    
    def extract_job_metadata(self, job) -> Dict[str, Any]:
        """
        从Job对象提取元数据，用于流的属性填充
        
        Args:
            job: simulator中的Job对象
            
        Returns:
            作业元数据字典
        """
        try:
            return {
                "model": job.application.name,
                "dataset": "SimulatedDataset",  # 模拟数据集名称
                "parameters": getattr(job.application, 'parameters', 1000000.0)
            }
        except Exception:
            # 返回默认值
            return {
                "model": "unknown",
                "dataset": "unknown", 
                "parameters": 1000000.0
            }
    
    def calculate_gradient_size(self, job, num_gpus: int) -> float:
        """
        计算单个流的梯度大小（MB）
        基于作业的模型参数和GPU数量
        
        Args:
            job: Job对象
            num_gpus: GPU数量
            
        Returns:
            单个流的数据量（MB）
        """
        try:
            # 从应用程序获取参数数量（如果可用）
            if hasattr(job.application, 'parameters'):
                total_params = job.application.parameters
            else:
                # 根据模型名称估算参数数量
                model_params = {
                    'bert': 110_000_000,      # BERT-Base
                    'cifar10': 11_000_000,    # ResNet等
                    'imagenet': 25_000_000,   # ResNet-50
                    'deepspeech2': 120_000_000, # DeepSpeech2
                    'ncf': 1_000_000,         # NCF
                    'yolov3': 62_000_000      # YOLOv3
                }
                total_params = model_params.get(job.application.name, 10_000_000)
            
            # 计算梯度总大小：参数数量 * 4字节（float32）/ (1024*1024) = MB
            total_gradient_mb = (total_params * 4) / (1024 * 1024)
            
            # Ring All-Reduce中每个流传输总梯度的 1/GPU数量
            flow_size_mb = total_gradient_mb / num_gpus
            
            return flow_size_mb
            
        except Exception as e:
            logger.warning(f"计算梯度大小时出错，使用默认值: {e}")
            return 10.0  # 默认10MB
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "device": str(self.device),
            "model_loaded": self.model is not None,
            "topology_loaded": self.clos_topology is not None
        }
    
    def validate_flow_data(self, flow: Dict) -> bool:
        """
        验证流数据格式是否正确
        
        Args:
            flow: 流数据字典
            
        Returns:
            是否格式正确
        """
        required_fields = ["flow_id", "path", "start_time", "flow_features"]
        
        if "inputs" in flow:
            inputs = flow["inputs"]
            return all(field in inputs for field in required_fields)
        else:
            return all(field in flow for field in required_fields)
    
    def batch_predict(self, flow_batches: List[List[Dict]], all_known_flows: List[Dict]) -> List[List[float]]:
        """
        批次预测多组流
        
        Args:
            flow_batches: 流批次列表
            all_known_flows: 所有已知流
            
        Returns:
            每批次的预测结果列表
        """
        results = []
        for batch in flow_batches:
            batch_result = self.predict_fct(batch, all_known_flows)
            results.append(batch_result)
        
        return results 