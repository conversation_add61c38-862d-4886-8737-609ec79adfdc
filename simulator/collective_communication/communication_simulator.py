#!/usr/bin/env python3
"""
通信模拟器 - 事件驱动模拟器主类
协调所有模拟组件，执行事件驱动的Ring All-Reduce通信模拟
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import copy

from .event_manager import EventManager, EventType
from .ring_all_reduce_tracker import RingAllReduceTracker, JobStatus
from .flow_generator import FlowGenerator

logger = logging.getLogger(__name__)

class CommunicationSimulator:
    """事件驱动通信模拟器主类"""
    
    def __init__(self, flow_generator: FlowGenerator = None, 
                 topology_manager = None, prediction_service = None,
                 enable_routing_optimizer: bool = False):
        """
        初始化通信模拟器
        
        Args:
            flow_generator: 流生成器实例
            topology_manager: 拓扑管理器实例
            prediction_service: 预测服务实例
            enable_routing_optimizer: 是否启用动态路由优化
        """
        self.flow_generator = flow_generator
        self.topology_manager = topology_manager
        self.prediction_service = prediction_service
        self.enable_routing_optimizer = enable_routing_optimizer
        
        # 核心组件
        self.event_manager = EventManager()
        self.tracker = RingAllReduceTracker(flow_generator, topology_manager)
        
        # 全局流状态维护
        self.all_known_flows = []  # 所有已知流的列表（持续累积）
        self.flow_id_to_index = {} # 流ID到索引的映射
        
        # 模拟状态
        self.current_interval_start = 0.0
        self.current_interval_duration = 60.0
        self.job_registry = {}  # {job_name: job_object}

        # 多次Ring All-Reduce控制
        self.enable_multiple_allreduces = True  # 是否启用多次通信
        self.completed_allreduces = {}  # {job_name: [completion_times]}
        self.allreduce_count = {}  # {job_name: count}
        self.current_placement = {}  # {job_name: placement}
        
        # 统计信息
        self.total_events_processed = 0
        self.total_flows_simulated = 0
        self.simulation_time_elapsed = 0.0
        
        # logger.info("通信模拟器初始化完成")
    
    def run(self, final_routing_plan: Dict[str, List[str]],
            active_jobs: List, interval_duration: float = 60.0) -> Dict[str, float]:
        """
        运行事件驱动通信模拟，支持多次Ring All-Reduce

        Args:
            final_routing_plan: 路由优化结果 {flow_id: path}
            active_jobs: 活跃作业列表
            interval_duration: 调度间隔时长（秒）

        Returns:
            平均通信时间结果 {job_name: average_communication_time}
        """
        logger.info(f"开始事件驱动通信模拟，调度间隔={interval_duration}秒，多次Ring All-Reduce={'启用' if self.enable_multiple_allreduces else '禁用'}")

        # 初始化模拟状态
        self.current_interval_duration = interval_duration
        self._initialize_simulation(active_jobs, final_routing_plan)

        if self.enable_multiple_allreduces:
            # 执行多次Ring All-Reduce模拟
            communication_times = self._execute_multiple_allreduces(active_jobs, interval_duration)
        else:
            # 执行单次Ring All-Reduce模拟（原有逻辑）
            self._prepare_initial_events(active_jobs)
            raw_communication_times = self._execute_event_loop(interval_duration)
            communication_times = {
                job_name: time
                for job_name, time in raw_communication_times.items()
            }
        
        # 清理和统计
        self._finalize_simulation()
        
        logger.debug(f"通信模拟完成，处理了 {self.total_events_processed} 个事件")
        return communication_times
    
    def _initialize_simulation(self, active_jobs: List, final_routing_plan: Dict[str, List[str]]):
        """
        初始化模拟状态
        
        Args:
            active_jobs: 活跃作业列表
            final_routing_plan: 路由方案
        """
        # 重置状态
        self.event_manager.reset()
        self.tracker.reset()
        self.job_registry.clear()
        
        # 注册作业并构建placement
        self.current_placement = {}
        for job in active_jobs:
            self.job_registry[job.name] = job

            # 构建placement字典
            if hasattr(job, 'placement') and job.placement:
                node_allocation = []
                current_node = 0
                for gpu_count in job.placement:
                    for _ in range(gpu_count):
                        node_allocation.append(current_node)
                    current_node += 1
                self.current_placement[job.name] = node_allocation
        
        # 应用路由方案到流
        self._apply_routing_plan(final_routing_plan)
        
        logger.info(f"初始化完成: {len(active_jobs)} 个作业, {len(self.all_known_flows)} 个流")
    
    def _apply_routing_plan(self, routing_plan: Dict[str, List[str]]):
        """
        应用路由方案到所有已知流
        
        Args:
            routing_plan: 路由方案
        """
        for flow in self.all_known_flows:
            flow_id = flow.get("flow_id")
            if flow_id in routing_plan:
                flow["path"] = routing_plan[flow_id]
                logger.debug(f"应用路由: {flow_id} -> {routing_plan[flow_id]}")
    
    def _prepare_initial_events(self, active_jobs: List):
        """
        准备初始事件（第一轮通信流的START_FLOW事件）
        
        Args:
            active_jobs: 活跃作业列表
        """
        for job in active_jobs:
            # 计算作业的计算时间
            computation_time = self._calculate_computation_time(job)
            
            # 第一轮通信的开始时间 = 间隔开始时间 + 计算时间
            first_round_start_time = self.current_interval_start + computation_time
            
            # 获取作业的第一轮流
            first_round_flows = self._get_job_first_round_flows(job)
            
            if first_round_flows:
                # 计算Ring大小
                ring_size = self._calculate_ring_size(job)
                
                # 初始化作业追踪
                self.tracker.initialize_job(job, ring_size, first_round_flows)
                
                # 关键修复：启动作业通信，记录第一轮开始时间
                self.tracker.start_job_communication(job.name, first_round_start_time)
                
                # 添加第一轮流的START_FLOW事件
                for flow in first_round_flows:
                    flow["start_time"] = first_round_start_time
                    self.event_manager.add_start_flow_event(first_round_start_time, flow)
                
                logger.debug(f"作业 {job.name} 第一轮 {len(first_round_flows)} 个流将在 {first_round_start_time:.4f} 开始")
    
    def _calculate_computation_time(self, job) -> float:
        """
        计算作业的计算时间
        
        Args:
            job: 作业对象
            
        Returns:
            计算时间（秒）
        """
        try:
            # 获取当前配置
            placement = tuple(filter(None, job.placement)) if hasattr(job, 'placement') else (1,)
            atomic_bsz = getattr(job, 'atomic_bsz', 32)
            
            # 使用应用的吞吐量模型计算
            step_time, sync_time = job.application.get_throughput(placement, atomic_bsz)
            compute_time = step_time - sync_time  # 纯计算时间
            
            return compute_time # 最少0.1秒
            
        except Exception as e:
            logger.warning(f"计算作业 {job.name} 计算时间时出错，使用默认值: {e}")
            return 1.0  # 默认1秒
    
    def _get_job_first_round_flows(self, job) -> List[Dict]:
        """
        获取作业第一轮的流
        
        Args:
            job: 作业对象
            
        Returns:
            第一轮流列表
        """
        # 从all_known_flows中筛选出该作业第一轮的流
        job_first_round_flows = []
        
        for flow in self.all_known_flows:
            if (flow.get("job_name") == job.name and 
                flow.get("round_idx") == 0):
                job_first_round_flows.append(flow)
        
        return job_first_round_flows
    
    def _calculate_ring_size(self, job) -> int:
        """
        计算作业的Ring大小 (即其占用的节点数量)
        
        Args:
            job: 作业对象，其 job.placement 属性是一个元组，
                 例如 (4, 4, 3)，代表作业使用了3个节点，
                 分别占用了4、4、3个GPU。
                 
        Returns:
            作业占用的节点数，作为Ring的大小。
        """
        try:
            # job.placement 是一个元组，其长度代表作业跨越的节点数。
            if hasattr(job, 'placement') and job.placement:
                # 正确的逻辑是直接获取 placement 元组的长度。
                return len(job.placement)
            else:
                # 如果没有分配 placement，则认为它不参与跨节点通信，Ring大小为1（或0，但1更安全）。
                return 1
        except Exception as e:
            # 增加日志记录，以便于调试未来可能出现的未知错误。
            logger.error(f"计算作业 {job.name} 的 Ring 大小时出现未知错误: {e}")
            return 1
    
    def _execute_event_loop(self, interval_duration: float) -> Dict[str, float]:
        """
        执行主事件循环
        
        Args:
            interval_duration: 间隔时长
            
        Returns:
            通信时间结果
        """
        interval_end_time = self.current_interval_start + interval_duration
        logger.info(f"=============开始事件循环=============：间隔时间 [{self.current_interval_start:.4f}, {interval_end_time:.4f}]")
        
        loop_iteration = 0
        while not self.event_manager.is_empty():
            loop_iteration += 1
            
            # 获取下一个事件
            next_event = self.event_manager.peek_next_event()
            
            # 检查是否超出调度间隔
            if next_event.timestamp > interval_end_time:
                logger.info(f"事件时间 {next_event.timestamp:.4f} 超出间隔结束时间 {interval_end_time:.4f}，保存状态并停止模拟")
                # 保存未完成的事件状态，而不是直接丢弃
                self._save_cross_interval_state(interval_end_time)
                break
            
            # 处理事件
            event = self.event_manager.get_next_event()
            logger.debug(f"循环 {loop_iteration}: 处理事件 {event.event_type.value} @ {event.timestamp:.4f}")
            
            self._process_event(event)
            self.total_events_processed += 1
            
            # 定期报告进度
            if loop_iteration % 10 == 0:
                remaining_events = self.event_manager.size()
                logger.debug(f"事件循环进度: 已处理 {loop_iteration} 个事件，队列剩余 {remaining_events} 个事件")
        
        logger.info(f"事件循环结束：总共处理了 {loop_iteration} 个事件")
        
        # 收集通信时间结果
        return self._collect_communication_times()
    
    def _process_event(self, event):
        """
        处理单个事件
        
        Args:
            event: 事件对象
        """
        if event.event_type == EventType.START_FLOW:
            self._handle_start_flow_event(event)
        elif event.event_type == EventType.FINISH_FLOW:
            self._handle_finish_flow_event(event)
        else:
            logger.warning(f"未知事件类型: {event.event_type}")
    
    def _handle_start_flow_event(self, event):
        """
        处理流开始事件
        
        Args:
            event: START_FLOW事件
        """
        flow = event.data["flow"]
        flow_id = flow["flow_id"]
        start_time = event.timestamp
        
        # 更新流状态
        self._update_flow_status(flow, "ACTIVE", start_time)
        
        # 通知追踪器
        self.tracker.start_flow(flow_id, flow, start_time)
        
        # 使用预测服务预测FCT
        predicted_fct = self._predict_flow_completion_time(flow)
        predicted_fct = 80 * predicted_fct / 1024 # 暂时处理
        # 将预测结果回填到all_known_flows中对应的流
        self._update_flow_predicted_fct(flow_id, predicted_fct)
        
        # 添加FINISH_FLOW事件
        finish_time = start_time + predicted_fct
        self.event_manager.add_finish_flow_event(finish_time, flow, predicted_fct)
        
        logger.debug(f"流 {flow_id} 开始，预测FCT={predicted_fct:.8f}秒，完成时间={finish_time:.8f}")
        self.total_flows_simulated += 1
    
    def _handle_finish_flow_event(self, event):
        """
        处理流完成事件
        
        Args:
            event: FINISH_FLOW事件
        """
        flow = event.data["flow"]
        flow_id = flow["flow_id"]
        end_time = event.timestamp
        
        # 更新流状态
        self._update_flow_status(flow, "COMPLETED", end_time)
        
        # 通知追踪器并检查是否触发新轮次
        next_round_flows = self.tracker.finish_flow(flow_id, end_time)
        
        # 关键修复：如果tracker没有返回流，主动生成下一轮流
        if not next_round_flows:
            # 获取该流对应的作业
            job_name = None
            for flow_key, job in self.tracker.flow_to_job.items():
                if flow_key == flow_id:
                    job_name = job
                    break
            
            if job_name and job_name in self.job_registry:
                job = self.job_registry[job_name]
                current_round = self.tracker.flow_to_round.get(flow_id, 0)
                
                # 检查该轮次是否完成，如果是则生成下一轮
                if self.tracker._is_round_completed(job_name, current_round):
                    next_round_flows = self.tracker.generate_next_round_flows(job, current_round)
                    logger.debug(f"主动生成作业 {job_name} 下一轮流: {len(next_round_flows)} 个流")
        
        # 如果有新轮次流，生成并添加它们的START_FLOW事件
        if next_round_flows:
            self._schedule_next_round_flows_with_dynamic_routing(next_round_flows, end_time)
        
        logger.debug(f"流 {flow_id} 完成，时间={end_time:.4f}")
    
    def _predict_flow_completion_time(self, flow: Dict) -> float:
        """
        使用预测服务预测流完成时间
        
        Args:
            flow: 流数据
            
        Returns:
            预测的完成时间（秒）
        """
        try:
            # 关键修复：归一化时间特征避免域偏移
            flow_for_prediction = flow.copy()
            
            # 将绝对开始时间转换为相对间隔时间（0-60秒范围）
            absolute_start_time = flow_for_prediction.get("start_time", 0.0)
            normalized_start_time = self._normalize_time_for_prediction(
                absolute_start_time, self.current_interval_start, self.current_interval_duration
            )
            flow_for_prediction["start_time"] = normalized_start_time
            
            logger.debug(f"流 {flow.get('flow_id')} 时间归一化: {absolute_start_time:.4f} -> {normalized_start_time:.4f}")
            
            # 准备预测输入
            flows_to_predict = [flow_for_prediction]
            
            # 调用预测服务
            predicted_fcts = self.prediction_service.predict_fct(flows_to_predict, self.all_known_flows)
            
            if predicted_fcts:
                return max(predicted_fcts[0], 0.001)  # 最少1毫秒
            else:
                return 1.0  # 默认1秒
                
        except Exception as e:
            logger.warning(f"预测流 {flow.get('flow_id')} FCT时出错，使用默认值: {e}")
            return 1.0
    
    def _normalize_time_for_prediction(self, absolute_time: float, interval_start: float, interval_duration: float) -> float:
        """
        将绝对时间转换为相对间隔时间，避免预测模型域偏移
        
        Args:
            absolute_time: 绝对模拟时间
            interval_start: 当前间隔开始时间
            interval_duration: 间隔持续时间
            
        Returns:
            归一化的相对时间（0-60秒范围内）
        """
        # 计算相对于当前间隔的时间偏移
        relative_time = absolute_time - interval_start
        
        # 归一化到0-60秒范围（模型训练数据的时间范围）
        normalized_time = (relative_time % interval_duration) / interval_duration * 60.0
        
        # 确保在有效范围内
        normalized_time = min(max(normalized_time, 0.0), 60.0)
        
        return normalized_time
    
    def _schedule_next_round_flows_with_dynamic_routing(self, next_round_flows: List[Dict], start_time: float):
        """
        调度下一轮流的START_FLOW事件，并进行动态路由优化
        
        Args:
            next_round_flows: 下一轮流列表
            start_time: 开始时间
        """
        if not next_round_flows:
            return
        
        # 为新流分配候选路径
        for flow in next_round_flows:
            self._assign_candidate_paths(flow)
        
        # 根据标志决定是否执行动态路由优化
        if self.enable_routing_optimizer:
            logger.debug("执行动态路由优化...")
            optimized_routing = self._perform_dynamic_routing_optimization(next_round_flows, start_time)
        else:
            logger.debug("跳过路由优化，使用默认ECMP路径")
            optimized_routing = {}
        
        # 应用优化后的路由并调度流
        for flow in next_round_flows:
            # 将新流添加到all_known_flows
            self._add_flow_to_known_flows(flow)
            
            # 应用优化后的路由
            flow_id = flow["flow_id"]
            if flow_id in optimized_routing:
                flow["path"] = optimized_routing[flow_id]
                logger.debug(f"流 {flow_id} 应用优化路由: {optimized_routing[flow_id]}")
            
            # 设置开始时间
            flow["start_time"] = start_time
            
            # 添加START_FLOW事件
            self.event_manager.add_start_flow_event(start_time, flow)
        
        if self.enable_routing_optimizer:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，应用动态路由优化")
        else:
            logger.debug(f"调度了 {len(next_round_flows)} 个下一轮流，开始时间={start_time:.4f}，使用默认ECMP路径")
    

    
    def _update_flow_status(self, flow: Dict, status: str, timestamp: float):
        """
        更新流状态
        
        Args:
            flow: 流数据
            status: 新状态
            timestamp: 时间戳
        """
        flow["status"] = status
        if status == "ACTIVE":
            flow["start_time"] = timestamp
        elif status == "COMPLETED":
            flow["end_time"] = timestamp
            if "start_time" in flow:
                flow["actual_duration"] = timestamp - flow["start_time"]
    
    def _add_flow_to_known_flows(self, flow: Dict):
        """
        将新流添加到已知流列表
        
        Args:
            flow: 流数据
        """
        flow_id = flow.get("flow_id")
        if flow_id not in self.flow_id_to_index:
            index = len(self.all_known_flows)
            self.all_known_flows.append(flow)
            self.flow_id_to_index[flow_id] = index
    
    def _update_flow_predicted_fct(self, flow_id: str, predicted_fct: float):
        """
        更新流的预测完成时间到all_known_flows中
        
        Args:
            flow_id: 流ID
            predicted_fct: 预测的完成时间
        """
        if flow_id in self.flow_id_to_index:
            index = self.flow_id_to_index[flow_id]
            self.all_known_flows[index]["predicted_fct"] = predicted_fct
            logger.debug(f"更新流 {flow_id} 的预测FCT: {predicted_fct:.4f}秒")
    
    def _collect_communication_times(self) -> Dict[str, float]:
        """
        收集所有作业的通信时间
        
        Returns:
            通信时间字典 {job_name: total_communication_time}
        """
        communication_times = {}
        
        for job_name in self.job_registry:
            comm_time = self.tracker.get_job_communication_time(job_name)
            communication_times[job_name] = comm_time
            
            # 获取作业详细进度信息
            progress = self.tracker.get_job_progress(job_name)
            current_round = self.tracker.get_current_round(job_name)
            total_rounds = self.tracker.job_total_rounds.get(job_name, 0)
            job_status = self.tracker.get_job_status(job_name)
            
            logger.info(f"作业 {job_name}: 通信时间={comm_time:.4f}秒, " +
                       f"轮次进度={current_round + 1}/{total_rounds}, " +
                       f"状态={job_status.value}, " +
                       f"完成度={progress['progress']:.2%}")
        
        return communication_times
    
    def _finalize_simulation(self):
        """最终化模拟，清理资源和记录统计"""
        self.simulation_time_elapsed = self.event_manager.get_current_time() - self.current_interval_start
        
        # 记录统计信息
        stats = self.get_simulation_statistics()
        logger.debug(f"模拟统计: {stats}")
    
    def set_all_known_flows(self, flows: List[Dict]):
        """
        设置所有已知流（从流生成器获得）
        
        Args:
            flows: 所有流的列表
        """
        self.all_known_flows = flows.copy()
        self.flow_id_to_index.clear()
        
        # 建立索引
        for i, flow in enumerate(self.all_known_flows):
            flow_id = flow.get("flow_id")
            if flow_id:
                self.flow_id_to_index[flow_id] = i
        
        logger.info(f"设置了 {len(self.all_known_flows)} 个已知流")
    
    def add_flows_to_known_flows(self, new_flows: List[Dict]):
        """
        向已知流列表添加新流
        
        Args:
            new_flows: 新流列表
        """
        for flow in new_flows:
            self._add_flow_to_known_flows(flow)
        
        logger.debug(f"添加了 {len(new_flows)} 个新流到已知流列表")
    
    def get_simulation_statistics(self) -> Dict[str, Any]:
        """
        获取模拟统计信息
        
        Returns:
            统计信息字典
        """
        event_stats = self.event_manager.get_statistics()
        tracker_stats = self.tracker.get_statistics()
        
        return {
            "total_events_processed": self.total_events_processed,
            "total_flows_simulated": self.total_flows_simulated,
            "simulation_time_elapsed": self.simulation_time_elapsed,
            "interval_duration": self.current_interval_duration,
            "total_known_flows": len(self.all_known_flows),
            "active_jobs": len(self.job_registry),
            "event_manager_stats": event_stats,
            "tracker_stats": tracker_stats
        }
    
    def save_simulation_state(self) -> Dict[str, Any]:
        """
        保存模拟状态（用于跨调度间隔持久化）
        
        Returns:
            状态字典
        """
        return {
            "current_interval_start": self.current_interval_start,
            "current_interval_duration": self.current_interval_duration,
            "all_known_flows": self.all_known_flows,
            "flow_id_to_index": self.flow_id_to_index,
            "total_events_processed": self.total_events_processed,
            "total_flows_simulated": self.total_flows_simulated,
            "event_manager_state": self.event_manager.save_state(),
            "tracker_state": self.tracker.save_state(),
            # 保存多次Ring All-Reduce的实际状态（不保存预估次数）
            "completed_allreduces": self.completed_allreduces,
            "allreduce_count": self.allreduce_count
        }
    
    def load_simulation_state(self, state: Dict[str, Any]):
        """
        从状态字典恢复模拟状态
        
        Args:
            state: 状态字典
        """
        self.current_interval_start = state.get("current_interval_start", 0.0)
        self.current_interval_duration = state.get("current_interval_duration", 60.0)
        self.all_known_flows = state.get("all_known_flows", [])
        self.flow_id_to_index = state.get("flow_id_to_index", {})
        self.total_events_processed = state.get("total_events_processed", 0)
        self.total_flows_simulated = state.get("total_flows_simulated", 0)

        # 恢复多次Ring All-Reduce的实际状态（不恢复预估次数）
        self.completed_allreduces = state.get("completed_allreduces", {})
        self.allreduce_count = state.get("allreduce_count", {})
        
        # 恢复子组件状态
        if "event_manager_state" in state:
            self.event_manager.load_state(state["event_manager_state"])
        
        if "tracker_state" in state:
            self.tracker.load_state(state["tracker_state"])
        
        logger.info("通信模拟器状态恢复完成")
    
    def _assign_candidate_paths(self, flow: Dict):
        """
        为流分配候选路径
        
        Args:
            flow: 流数据
        """
        src_host = flow.get("src_host")
        dst_host = flow.get("dst_host")
        
        if src_host and dst_host and self.topology_manager:
            try:
                candidate_paths = self.topology_manager.get_ecmp_paths(src_host, dst_host)
                flow["candidate_paths"] = candidate_paths
                
                # 设置默认路径
                if candidate_paths:
                    flow["path"] = candidate_paths[0]
                    logger.debug(f"流 {flow.get('flow_id')} 分配候选路径: {len(candidate_paths)} 条")
                else:
                    logger.warning(f"流 {flow.get('flow_id')} 未找到候选路径")
                    flow["candidate_paths"] = []
                    flow["path"] = []
            except Exception as e:
                logger.error(f"为流 {flow.get('flow_id')} 分配路径时出错: {e}")
                flow["candidate_paths"] = []
                flow["path"] = []
    
    def _perform_dynamic_routing_optimization(self, new_flows: List[Dict], start_time: float) -> Dict[str, List[str]]:
        """
        执行动态路由优化
        
        Args:
            new_flows: 新生成的流列表
            start_time: 流开始时间
            
        Returns:
            优化后的路由方案 {flow_id: path}
        """
        if not new_flows:
            return {}
        
        try:
            # 检测路径冲突
            conflict_flows = self._detect_path_conflicts(new_flows, start_time)
            
            if not conflict_flows:
                logger.debug("未检测到路径冲突，使用默认路由")
                return {}
            
            logger.info(f"检测到 {len(conflict_flows)} 个流存在路径冲突，触发动态路由优化")
            
            # 调用路由优化器进行增量优化
            if hasattr(self, 'routing_optimizer') and self.routing_optimizer:
                # 构建优化输入：冲突流 + 相关背景流
                optimization_flows = self._prepare_optimization_flows(conflict_flows, start_time)
                
                # 执行优化
                self.routing_optimizer.set_current_time(start_time)
                optimized_routing = self.routing_optimizer.solve(optimization_flows)
                
                logger.info(f"动态路由优化完成，优化了 {len(optimized_routing)} 个流的路由")
                return optimized_routing
            else:
                logger.warning("路由优化器不可用，使用默认路由")
                return {}
                
        except Exception as e:
            logger.error(f"动态路由优化失败: {e}")
            return {}
    
    def _detect_path_conflicts(self, new_flows: List[Dict], start_time: float) -> List[Dict]:
        """
        检测新流与现有流的路径冲突
        
        Args:
            new_flows: 新流列表
            start_time: 开始时间
            
        Returns:
            存在冲突的流列表
        """
        conflict_flows = []
        decision_window = 2.0  # 冲突检测时间窗口（秒）
        
        # 收集当前活跃流和即将开始的流的链路占用
        active_links = set()
        future_links = set()
        
        for flow in self.all_known_flows:
            flow_start_time = flow.get("start_time", 0.0)
            flow_status = flow.get("status", "PLANNED")
            flow_path = flow.get("path", [])
            
            # 当前活跃流的链路
            if flow_status == "ACTIVE":
                active_links.update(self._extract_path_links(flow_path))
            
            # 决策窗口内的未来流链路
            elif (flow_status == "PLANNED" and 
                  start_time <= flow_start_time <= start_time + decision_window):
                future_links.update(self._extract_path_links(flow_path))
        
        # 检查每个新流是否与现有流冲突
        for flow in new_flows:
            flow_path = flow.get("path", [])
            flow_links = self._extract_path_links(flow_path)
            
            # 检查与活跃流或未来流的冲突
            if flow_links.intersection(future_links):
                conflict_flows.append(flow)
                logger.debug(f"流 {flow.get('flow_id')} 检测到路径冲突")
        
        return conflict_flows
    
    def _extract_path_links(self, path: List[str]) -> set:
        """
        从路径中提取链路集合
        
        Args:
            path: 路径节点列表
            
        Returns:
            链路集合 {(src, dst), ...}
        """
        links = set()
        if len(path) > 1:
            for i in range(len(path) - 1):
                links.add((path[i], path[i + 1]))
        return links
    
    def _prepare_optimization_flows(self, conflict_flows: List[Dict], start_time: float) -> List[Dict]:
        """
        准备路由优化的输入流集合
        
        Args:
            conflict_flows: 冲突流
            start_time: 开始时间
            
        Returns:
            优化输入流列表
        """
        optimization_flows = []
        decision_window = 2.0
        
        # 添加冲突流
        optimization_flows.extend(conflict_flows)
        
        # 添加相关的背景流和未来流
        for flow in self.all_known_flows:
            flow_start_time = flow.get("start_time", 0.0)
            flow_status = flow.get("status", "PLANNED")
            
            # 活跃流或决策窗口内的未来流
            if (flow_status == "ACTIVE" or 
                (flow_status == "PLANNED" and 
                 start_time <= flow_start_time <= start_time + decision_window)):
                
                # 避免重复添加
                if flow not in optimization_flows:
                    optimization_flows.append(flow)
        
        return optimization_flows
    
    def set_routing_optimizer(self, routing_optimizer):
        """
        设置路由优化器实例
        
        Args:
            routing_optimizer: 路由优化器实例
        """
        self.routing_optimizer = routing_optimizer
        logger.info("动态路由优化器已设置")
    
    def reset_simulation(self):
        """重置模拟器到初始状态"""
        self.event_manager.reset()
        self.tracker.reset()
        self.all_known_flows.clear()
        self.flow_id_to_index.clear()
        self.job_registry.clear()
        
        self.current_interval_start = 0.0
        self.current_interval_duration = 60.0
        self.total_events_processed = 0
        self.total_flows_simulated = 0
        self.simulation_time_elapsed = 0.0
        
        logger.info("通信模拟器已重置")

    def _save_cross_interval_state(self, interval_end_time: float):
        """
        保存跨间隔的状态信息

        Args:
            interval_end_time: 间隔结束时间
        """
        # 计算部分完成的通信时间
        partial_communication_times = {}

        for job_name in self.job_registry:
            # 获取作业在当前间隔内的部分通信时间
            job_start_time = None
            if (job_name in self.tracker.job_round_start_times and
                0 in self.tracker.job_round_start_times[job_name]):
                job_start_time = self.tracker.job_round_start_times[job_name][0]

            if job_start_time is not None and job_start_time < interval_end_time:
                # 计算到间隔结束时的部分通信时间
                partial_time = interval_end_time - job_start_time
                partial_communication_times[job_name] = partial_time
                logger.debug(f"作业 {job_name} 部分通信时间: {partial_time:.4f}秒")
            else:
                partial_communication_times[job_name] = 0.0

        # 将部分时间存储到tracker中
        for job_name, partial_time in partial_communication_times.items():
            if job_name in self.tracker.job_communication_times:
                self.tracker.job_communication_times[job_name] = partial_time

        logger.info(f"保存跨间隔状态: {len(partial_communication_times)} 个作业的部分通信时间")



    def _execute_multiple_allreduces(self, active_jobs: List, interval_duration: float) -> Dict[str, float]:
        """
        执行多次Ring All-Reduce循环

        Args:
            active_jobs: 活跃作业列表
            interval_duration: 间隔时长

        Returns:
            平均通信时间字典
        """
        logger.debug(f"开始多次Ring All-Reduce模拟，作业数量: {len(active_jobs)}")

        # 初始化多次通信状态
        self.completed_allreduces = {}
        self.allreduce_count = {}

        # 为每个作业初始化状态（移除错误的预估逻辑）
        for job in active_jobs:
            self.allreduce_count[job.name] = 0
            self.completed_allreduces[job.name] = []

            logger.debug(f"作业 {job.name}: 开始迭代式多次Ring All-Reduce，基于时间边界动态控制")

        # 启动第一轮Ring All-Reduce
        self._prepare_initial_events(active_jobs)

        # 执行事件循环，支持多次Ring All-Reduce
        self._execute_multiple_allreduce_loop(interval_duration)

        # 计算平均通信时间
        average_times = self._calculate_average_communication_times()

        logger.info(f"多次Ring All-Reduce模拟完成，结果: {average_times}")
        return average_times

    def _execute_multiple_allreduce_loop(self, interval_duration: float) -> None:
        """
        执行多次Ring All-Reduce的事件循环

        Args:
            interval_duration: 间隔时长
        """
        interval_end_time = self.current_interval_start + interval_duration
        logger.debug(f"开始多次Ring All-Reduce事件循环，间隔时间 [{self.current_interval_start:.4f}, {interval_end_time:.4f}]")

        loop_iteration = 0
        while not self.event_manager.is_empty():
            loop_iteration += 1

            # 获取下一个事件
            next_event = self.event_manager.peek_next_event()

            # 检查是否超出调度间隔
            if next_event.timestamp > interval_end_time:
                logger.info(f"事件时间 {next_event.timestamp:.4f} 超出间隔结束时间 {interval_end_time:.4f}，保存状态并停止模拟")
                self._save_cross_interval_state(interval_end_time)
                break

            # 处理事件
            event = self.event_manager.get_next_event()
            logger.debug(f"循环 {loop_iteration}: 处理事件 {event.event_type.value} @ {event.timestamp:.4f}")

            self._process_event_with_restart_check(event, interval_end_time)
            self.total_events_processed += 1

            # 定期报告进度
            if loop_iteration % 10 == 0:
                remaining_events = self.event_manager.size()
                logger.debug(f"多次Ring All-Reduce循环进度: 已处理 {loop_iteration} 个事件，队列剩余 {remaining_events} 个事件")

        logger.debug(f"多次Ring All-Reduce事件循环结束：总共处理了 {loop_iteration} 个事件")

    def _process_event_with_restart_check(self, event, interval_end_time: float) -> None:
        """
        处理事件并检查是否需要重启Ring All-Reduce

        Args:
            event: 事件对象
            interval_end_time: 间隔结束时间
        """
        # 处理原有事件逻辑
        self._process_event(event)

        # 如果是FINISH_FLOW事件，检查是否需要重启Ring All-Reduce
        if event.event_type == EventType.FINISH_FLOW:
            self._check_and_restart_allreduce(event, interval_end_time)

    def _check_and_restart_allreduce(self, finish_event, interval_end_time: float) -> None:
        """
        检查Ring All-Reduce完成情况并决定是否重启

        Args:
            finish_event: FINISH_FLOW事件
            interval_end_time: 间隔结束时间
        """
        # 正确访问flow_id - 它存储在event.data中
        flow_id = finish_event.data.get("flow_id")
        if not flow_id or flow_id not in self.tracker.flow_to_job:
            return

        job_name = self.tracker.flow_to_job[flow_id]
        job_status = self.tracker.get_job_status(job_name)

        # 检查作业是否完成了一次完整的Ring All-Reduce
        if job_status == JobStatus.COMPLETED:
            self._handle_allreduce_completion(job_name, finish_event.timestamp, interval_end_time)

    def _handle_allreduce_completion(self, job_name: str, completion_time: float, interval_end_time: float) -> None:
        """
        处理Ring All-Reduce完成，基于时间边界动态决定是否继续

        Args:
            job_name: 作业名称
            completion_time: 完成时间
            interval_end_time: 间隔结束时间
        """
        # 记录本次Ring All-Reduce的通信时间
        comm_time = self.tracker.get_job_communication_time(job_name)
        self.completed_allreduces[job_name].append(comm_time)
        self.allreduce_count[job_name] += 1

        current_count = self.allreduce_count[job_name]

        # 动态计算下次开始时间
        next_start_time = completion_time + self._calculate_computation_time(self.job_registry[job_name])

        logger.debug(f"作业 {job_name} 完成第 {current_count} 次Ring All-Reduce，"
                   f"通信时间={comm_time:.4f}秒，下次开始时间={next_start_time:.4f}秒")

        # 纯粹基于时间边界判断是否继续（移除预估次数依赖）
        if next_start_time < interval_end_time:
            logger.debug(f"作业 {job_name} 在时间边界内，启动下一次Ring All-Reduce")
            self._start_new_allreduce_cycle(job_name, next_start_time)
        else:
            logger.info(f"作业 {job_name} 下次开始时间 {next_start_time:.4f} "
                       f"超出间隔结束时间 {interval_end_time:.4f}，停止启动新周期")

    def _start_new_allreduce_cycle(self, job_name: str, start_time: float) -> None:
        """
        启动新的Ring All-Reduce周期

        Args:
            job_name: 作业名称
            start_time: 开始时间
        """
        job = self.job_registry[job_name]

        # 重置作业的追踪状态
        self.tracker.reset_job_state(job_name)

        # 重新生成第一轮流 - 使用generate_next_round_flows方法，轮次-1表示生成第0轮
        first_round_flows = self.flow_generator.generate_next_round_flows(
            job, -1, self.topology_manager
        )

        if first_round_flows:
            # 重新初始化作业追踪
            ring_size = self._calculate_ring_size(job)
            self.tracker.initialize_job(job, ring_size, first_round_flows)
            self.tracker.start_job_communication(job_name, start_time)

            # 添加第一轮流的START_FLOW事件
            for flow in first_round_flows:
                flow["start_time"] = start_time
                self.event_manager.add_start_flow_event(start_time, flow)

            logger.debug(f"作业 {job_name} 启动新的Ring All-Reduce周期，开始时间={start_time:.4f}，"
                       f"第一轮流数量={len(first_round_flows)}")
        else:
            logger.warning(f"作业 {job_name} 无法生成新周期的第一轮流")

    def _calculate_average_communication_times(self) -> Dict[str, float]:
        """
        计算所有作业的平均通信时间

        Returns:
            平均通信时间字典
        """
        average_times = {}

        for job_name, comm_times in self.completed_allreduces.items():
            if comm_times:
                average_time = sum(comm_times) / len(comm_times)
                average_times[job_name] = average_time

                logger.debug(f"作业 {job_name}: 完成 {len(comm_times)} 次Ring All-Reduce，"
                           f"通信时间列表={[f'{t:.4f}' for t in comm_times]}，"
                           f"平均时间={average_time:.4f}秒")
            else:
                # 如果没有完成任何Ring All-Reduce，尝试获取当前进行中的部分时间
                partial_time = self.tracker.get_job_communication_time(job_name)
                average_times[job_name] = partial_time

                logger.warning(f"作业 {job_name}: 未完成任何Ring All-Reduce，"
                              f"使用部分通信时间={partial_time:.4f}秒")

        return average_times

    def _calculate_ring_size(self, job) -> int:
        """
        计算作业的Ring大小

        Args:
            job: 作业对象

        Returns:
            Ring大小
        """
        if hasattr(job, 'placement') and job.placement:
            # Ring大小等于跨越的节点数
            return len(job.placement)
        return 1

    def _calculate_computation_time(self, job) -> float:
        """
        计算作业的计算时间（两次通信之间的间隔）
        基于实际的训练特性，而不是预估

        Args:
            job: 作业对象

        Returns:
            计算时间（秒）
        """
        try:
            # 基于作业类型和配置的合理估算
            base_compute_time = self._get_base_compute_time(job)
            return max(base_compute_time, 0.1)  # 最少0.1秒

        except Exception as e:
            logger.warning(f"计算作业 {job.name} 计算时间时出错，使用默认值: {e}")
            return 1.0  # 默认1秒

    def _get_base_compute_time(self, job) -> float:
        """
        基于作业特性估算基础计算时间

        Args:
            job: 作业对象

        Returns:
            基础计算时间（秒）
        """
        try:
            # 基于模型类型的计算时间估算
            model_compute_times = {
                'bert': 2.0,        # BERT模型计算较重
                'cifar10': 0.5,     # CIFAR10模型较轻
                'imagenet': 1.5,    # ImageNet模型中等
                'deepspeech2': 1.8, # DeepSpeech2模型较重
                'ncf': 0.3,         # NCF模型很轻
                'yolov3': 1.2       # YOLOv3模型中等
            }

            model_name = getattr(job.application, 'name', 'unknown')
            base_time = model_compute_times.get(model_name, 1.0)

            # 根据GPU数量调整（更多GPU可能意味着更大的批大小，需要更多计算时间）
            if hasattr(job, 'placement') and job.placement:
                total_gpus = sum(job.placement)
                # GPU数量越多，计算时间可能稍微增加
                gpu_factor = 1.0 + (total_gpus - 1) * 0.1
                base_time *= gpu_factor

            return base_time

        except Exception as e:
            logger.warning(f"计算作业 {job.name} 基础计算时间时出错: {e}")
            return 1.0

    def set_interval_start_time(self, start_time: float):
        """
        设置当前调度间隔的开始时间
        
        Args:
            start_time: 开始时间
        """
        self.current_interval_start = start_time
        self.event_manager.set_current_time(start_time) 