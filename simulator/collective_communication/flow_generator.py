#!/usr/bin/env python3
"""
流生成器 - 从placement生成Ring All-Reduce通信的P2P流
严格按照Ring All-Reduce算法生成2*(N-1)轮的点对点流
"""

import logging
from typing import List, Dict, Any, Tuple
from collections import defaultdict
import itertools

logger = logging.getLogger(__name__)

class FlowGenerator:
    """Ring All-Reduce流生成器"""
    
    def __init__(self):
        """初始化流生成器"""
        self.flow_id_counter = 0
        logger.info("流生成器初始化完成")
    
    def generate_all_potential_flows(self, active_jobs: List, placement: Dict[str, List], 
                                   topology_manager) -> List[Dict]:
        """
        为所有活跃作业生成Ring All-Reduce通信所需的所有潜在P2P流
        
        Args:
            active_jobs: 活跃作业列表
            placement: 作业资源分配 {job_name: [node_indices]}
            topology_manager: 拓扑管理器实例
            
        Returns:
            所有潜在流的列表
        """
        all_flows = []
        
        for job in active_jobs:
            job_name = job.name
            if job_name not in placement or not placement[job_name]:
                continue
                
            # 生成该作业的所有流
            job_flows = self._generate_job_flows(job, placement[job_name], topology_manager)
            all_flows.extend(job_flows)
        
        logger.info(f"共生成 {len(all_flows)} 个潜在流，涉及 {len(active_jobs)} 个作业")
        return all_flows
    
    def _generate_job_flows(self, job, allocation: List[int], topology_manager) -> List[Dict]:
        """
        为单个作业生成Ring All-Reduce流
        
        Args:
            job: 作业对象
            allocation: GPU分配列表，如[0, 0, 1, 1]表示节点0有2个GPU，节点1有2个GPU
            topology_manager: 拓扑管理器
            
        Returns:
            该作业的所有流列表
        """
        flows = []
        
        # 提取跨节点的GPU分配
        node_gpus = self._extract_inter_node_gpus(allocation)
        
        if len(node_gpus) <= 1:
            # 单节点或无分配，无需跨节点通信
            logger.debug(f"作业 {job.name} 为单节点分配，无需生成跨节点流")
            return flows
        
        # 建立Ring拓扑
        ring_order = self._establish_ring_topology(node_gpus)
        ring_size = len(ring_order)
        
        # 计算流的基本属性
        job_metadata = self._extract_job_metadata(job)
        flow_size_mb = self._calculate_flow_size(job, ring_size)
        
        # 生成Ring All-Reduce的2*(N-1)轮流
        total_rounds = 2 * (ring_size - 1)
        
        for round_idx in range(total_rounds):
            round_flows = self._generate_round_flows(
                job, ring_order, round_idx, flow_size_mb, 
                job_metadata, topology_manager
            )
            flows.extend(round_flows)
        
        logger.info(f"作业 {job.name} 生成了 {len(flows)} 个流，{total_rounds} 轮通信")
        return flows
    
    def _extract_inter_node_gpus(self, allocation: List[int]) -> Dict[int, int]:
        """
        提取跨节点GPU分配
        
        Args:
            allocation: GPU分配列表
            
        Returns:
            节点GPU映射 {node_id: gpu_count}
        """
        node_gpus = defaultdict(int)
        
        for node_id in allocation:
            if node_id is not None and node_id >= 0:
                node_gpus[node_id] += 1
        
        # 只保留有GPU分配的节点
        return {node_id: count for node_id, count in node_gpus.items() if count > 0}
    
    def _establish_ring_topology(self, node_gpus: Dict[int, int]) -> List[str]:
        """
        建立Ring拓扑，确定节点在环中的顺序
        
        Args:
            node_gpus: 节点GPU分配
            
        Returns:
            Ring中节点的有序列表（主机名）
        """
        # 将节点ID转换为主机名并排序
        node_ids = sorted(node_gpus.keys())
        
        # 生成主机名（节点ID直接映射到主机名）
        ring_order = []
        for node_id in node_ids:
            # 每个节点选择一个代表性主机进行Ring通信
            # 根据实际拓扑，每个节点对应一组主机
            # 简化映射：节点ID直接对应主机名（节点0->H1, 节点1->H2, ...）
            host_name = f"H{node_id + 1}"
            ring_order.append(host_name)
        
        logger.debug(f"建立Ring拓扑: {ring_order}")
        return ring_order
    
    def _calculate_flow_size(self, job, ring_size: int) -> float:
        """
        计算单个流的数据大小（MB）
        
        Args:
            job: 作业对象
            ring_size: Ring大小
            
        Returns:
            流大小（MB）
        """
        try:
            # 根据模型名称估算参数数量
            model_params = {
                'bert': 110_000_000,      # BERT-Base
                'cifar10': 11_000_000,    # ResNet等
                'imagenet': 25_000_000,   # ResNet-50
                'deepspeech2': 120_000_000, # DeepSpeech2
                'ncf': 1_000_000,         # NCF
                'yolov3': 62_000_000      # YOLOv3
            }
            
            total_params = model_params.get(job.application.name, 10_000_000)
            
            # 计算梯度总大小：参数数量 * 4字节（float32）
            total_gradient_bytes = total_params * 4
            total_gradient_mb = total_gradient_bytes / (1024 * 1024)
            
            # Ring All-Reduce中每个流传输 总梯度/Ring大小 的数据
            flow_size_mb = total_gradient_mb / ring_size
            
            return flow_size_mb
            
        except Exception as e:
            logger.warning(f"计算流大小时出错，使用默认值: {e}")
            return 10.0  # 默认10MB
    
    def _extract_job_metadata(self, job) -> Dict[str, Any]:
        """
        提取作业元数据
        
        Args:
            job: 作业对象
            
        Returns:
            作业元数据
        """
        try:
            return {
                "model": job.application.name,
                "dataset": "SimulatedDataset",
                "parameters": getattr(job.application, 'parameters', 1000000.0)
            }
        except Exception:
            return {
                "model": "unknown",
                "dataset": "unknown",
                "parameters": 1000000.0
            }
    
    def _generate_round_flows(self, job, ring_order: List[str], round_idx: int, 
                            flow_size_mb: float, job_metadata: Dict, 
                            topology_manager) -> List[Dict]:
        """
        生成单轮Ring All-Reduce的流
        
        Args:
            job: 作业对象
            ring_order: Ring节点顺序
            round_idx: 轮次索引（0到2*(N-1)-1）
            flow_size_mb: 流大小
            job_metadata: 作业元数据
            topology_manager: 拓扑管理器
            
        Returns:
            该轮的流列表
        """
        flows = []
        ring_size = len(ring_order)
        
        if ring_size <= 1:
            return flows
        
        # Ring All-Reduce分为两个阶段：
        # 阶段1: Reduce-Scatter (轮次 0 到 N-2)
        # 阶段2: All-Gather (轮次 N-1 到 2*N-3)
        
        is_reduce_scatter = round_idx < (ring_size - 1)
        
        # 在每一轮中，每个节点都向其Ring中的下一个节点发送数据
        for i, src_host in enumerate(ring_order):
            # 确定目标节点（Ring中的下一个节点）
            dst_idx = (i + 1) % ring_size
            dst_host = ring_order[dst_idx]
            
            # 跳过同一主机的"流"
            if src_host == dst_host:
                continue
            
            # 生成流ID
            flow_id = f"{job.name}_round{round_idx}_src{src_host}_dst{dst_host}"
            
            # 获取候选路径
            try:
                candidate_paths = topology_manager.get_ecmp_paths(src_host, dst_host)
            except Exception as e:
                logger.warning(f"获取路径失败 {src_host}->{dst_host}: {e}")
                candidate_paths = []
            
            # 创建流对象
            flow = {
                "flow_id": flow_id,
                "job_name": job.name,
                "round_idx": round_idx,
                "phase": "reduce_scatter" if is_reduce_scatter else "all_gather",
                "src_host": src_host,
                "dst_host": dst_host,
                "ringallreduce_group_size": ring_size,
                "flow_features": [flow_size_mb],
                "candidate_paths": candidate_paths,
                "path": candidate_paths[0] if candidate_paths else [],  # 默认第一条路径
                "start_time": 0.0,  # 将在模拟时设置
                "model": job_metadata["model"],
                "dataset": job_metadata["dataset"],
                "parameters": job_metadata["parameters"],
                "status": "PLANNED"  # 初始状态
            }
            
            flows.append(flow)
            self.flow_id_counter += 1
        
        return flows
    
    def generate_next_round_flows(self, job, current_round: int, topology_manager) -> List[Dict]:
        """
        为作业生成下一轮的流（在事件驱动模拟中使用）
        
        Args:
            job: 作业对象
            current_round: 当前完成的轮次
            topology_manager: 拓扑管理器
            
        Returns:
            下一轮的流列表
        """
        logger.debug(f"为作业 {job.name} 生成下一轮流，当前完成轮次={current_round}")
        
        # 重新计算作业的Ring拓扑
        # 从job.placement重建正确的allocation
        # job.placement = (2, 2) 表示节点0有2GPU，节点1有2GPU
        # 需要转换为 allocation = [0, 0, 1, 1]
        allocation = []
        if hasattr(job, 'placement') and job.placement:
            current_node = 0
            for gpu_count in job.placement:
                for _ in range(gpu_count):
                    allocation.append(current_node)
                current_node += 1
        
        logger.debug(f"作业 {job.name} placement={job.placement}, 重建allocation={allocation}")
        
        node_gpus = self._extract_inter_node_gpus(allocation)
        logger.debug(f"提取的节点GPU分布: {node_gpus}")
        
        if len(node_gpus) <= 1:
            logger.debug(f"作业 {job.name} 只有 {len(node_gpus)} 个节点，无需生成跨节点流")
            return []
        
        ring_order = self._establish_ring_topology(node_gpus)
        ring_size = len(ring_order)
        total_rounds = 2 * (ring_size - 1)
        
        next_round = current_round + 1
        logger.debug(f"下一轮次={next_round}, 总轮次={total_rounds}, Ring大小={ring_size}")
        
        if next_round >= total_rounds:
            logger.debug(f"所有轮次已完成，不生成更多流")
            return []  # 所有轮次已完成
        
        # 生成下一轮的流
        job_metadata = self._extract_job_metadata(job)
        flow_size_mb = self._calculate_flow_size(job, ring_size)
        
        logger.debug(f"生成轮次 {next_round} 的流，Ring顺序={ring_order}")
        
        next_round_flows = self._generate_round_flows(
            job, ring_order, next_round, flow_size_mb,
            job_metadata, topology_manager
        )
        
        logger.debug(f"为作业 {job.name} 轮次 {next_round} 生成了 {len(next_round_flows)} 个流")
        
        return next_round_flows
    
    def validate_flow(self, flow: Dict) -> bool:
        """
        验证流数据的完整性
        
        Args:
            flow: 流数据
            
        Returns:
            是否有效
        """
        required_fields = [
            "flow_id", "job_name", "src_host", "dst_host",
            "flow_features", "path", "ringallreduce_group_size"
        ]
        
        return all(field in flow for field in required_fields)
    
    def get_flow_stats(self, flows: List[Dict]) -> Dict[str, Any]:
        """
        获取流统计信息
        
        Args:
            flows: 流列表
            
        Returns:
            统计信息
        """
        if not flows:
            return {"total_flows": 0}
        
        job_counts = defaultdict(int)
        round_counts = defaultdict(int)
        total_data = 0.0
        
        for flow in flows:
            job_counts[flow.get("job_name", "unknown")] += 1
            round_counts[flow.get("round_idx", -1)] += 1
            flow_features = flow.get("flow_features", [0.0])
            if flow_features:
                total_data += flow_features[0]
        
        return {
            "total_flows": len(flows),
            "unique_jobs": len(job_counts),
            "unique_rounds": len(round_counts),
            "total_data_mb": total_data,
            "avg_flow_size_mb": total_data / len(flows) if flows else 0.0,
            "job_distribution": dict(job_counts),
            "round_distribution": dict(round_counts)
        } 